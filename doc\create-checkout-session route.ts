import { NextRequest, NextResponse } from 'next/server';
// import { auth } from '@clerk/nextjs/server'; // <-- 注释掉 auth
import { currentUser } from '@clerk/nextjs/server'; // <-- 导入 currentUser
import { prisma } from '@/lib/prisma'; // 根据需要调整路径
import { headers } from 'next/headers'; // 导入 headers
import fetch from 'node-fetch'; // 最后解决了问题，说明 undici 与 node-fetch 有差异。

const BASIC_PLAN_PRODUCT_ID = process.env.BASIC_PLAN_PRODUCT_ID!;

// 为 Creem API 的错误响应定义一个基本接口
interface CreemErrorResponse {
  error?: {
    message?: string;
    [key: string]: any; // 允许其他属性
  };
  message?: string;
  [key: string]: any; // 允许其他属性
}

export async function POST(request: NextRequest) {
  console.log("--- Enter /api/creem/create-checkout-session ---");

  // --- 1. 用户认证 ---
  // 使用 Clerk 的 currentUser() 获取当前用户信息，并提取 userId。
  // 如果未找到 userId，则表示用户未认证，返回 401 Unauthorized。
  const headersList = headers();
  const authorizationHeader = headersList.get('authorization');
  console.log("Received Authorization Header:", authorizationHeader);

  // --- 使用 currentUser() ---
  const userFromCurrentUser = await currentUser(); // <-- 使用 currentUser() 获取用户对象
  const userId = userFromCurrentUser?.id; // <-- 从用户对象中获取 ID
  // 打印 currentUser() 的结果 (注意：它返回用户对象或 null)
  console.log("Clerk currentUser() 结果:", userFromCurrentUser ? { userId: userFromCurrentUser.id, email: userFromCurrentUser.emailAddresses[0]?.emailAddress } : null);
  // --- 结束修改 ---

  // 严格检查 userId
  if (!userId) {
    console.error("Authorization failed: No userId found via currentUser(). Returning 401.");
    return NextResponse.json({ success: false, error: 'Unauthorized: User ID not found via currentUser()' }, { status: 401 });
  }

  console.log(`Authentication successful via currentUser() for user: ${userId}`);

  try {
     // --- 2. 获取用户数据库信息 ---
    // 根据认证后的 userId，在本地数据库 (Prisma) 中查找对应的用户记录。
    // 主要获取用户的 email 和已有的 creemCustomerId (如果存在)。
    // 如果数据库中找不到用户记录，记录警告，但暂时允许继续（依赖后续步骤处理）。   
    const dbUser = await prisma.user.findUnique({ // 重命名变量以避免与 currentUser 返回的 user 混淆
      where: { clerkUserId: userId },
      select: {
        email: true,
        creemCustomerId: true,
      }
    });

    if (!dbUser) {
      // 用户在 Clerk 认证成功，但在本地数据库找不到
      // 这个错误需要通过用户同步机制解决 (Webhook)
      console.error(`Authenticated user ${userId} not found in local database.`);
      // 你可能需要先确保用户存在于数据库才能创建订阅
      // return NextResponse.json({ success: false, error: 'User profile setup incomplete. Please try again shortly.' }, { status: 400 }); // 或者返回 400 Bad Request
      // 暂时允许继续，但需要注意这可能导致 customerId 或 email 缺失
       console.warn(`Proceeding without local DB record for ${userId}. Customer creation might rely solely on Clerk email.`);
    }
    // --- 结束 2. 获取用户数据库信息 ---

    // --- 3. 确定客户标识符 ---
    // 确定用于 Creem API 调用的客户信息。
    // 优先使用数据库中存储的 creemCustomerId。
    // 如果没有 creemCustomerId，则尝试使用从 Clerk 获取的主邮箱地址。
    // 如果两者都无法获取，则无法识别客户，返回 400 Bad Request。
    const creemCustomerId = dbUser?.creemCustomerId;
    const userEmail = dbUser?.email ?? userFromCurrentUser?.emailAddresses?.find(e => e.id === userFromCurrentUser.primaryEmailAddressId)?.emailAddress; // 尝试从 Clerk 获取主邮箱

    if (!creemCustomerId && !userEmail) {
      console.error(`无法继续：在数据库中未找到 Creem 客户 ID，且未找到 Clerk 用户 ${userId} 的主邮箱`);
       return NextResponse.json({ success: false, error: 'Unable to identify customer for subscription.' }, { status: 400 });
    }
    // --- 结束 3. 确定客户标识符 ---

    // --- 4. 准备 Creem API 参数 ---
    // 构建调用 Creem /v1/checkouts API 所需的参数对象。
    // 包括产品 ID、用于追踪的 request_id (使用 userId)、成功和取消的回调 URL、
    // 客户信息 (优先使用 ID，其次是 email) 以及元数据 (包含 clerkUserId 和订阅层级)。

    const appUrl = process.env.NEXT_PUBLIC_APP_URL;
    if (!appUrl) {
        throw new Error("NEXT_PUBLIC_APP_URL is not set in environment variables.");
    }

    // 解析前端传来 priceId 和 planType，并基于此构建 params
    const { priceId, planType } = await request.json();
    if (!priceId) {
      return NextResponse.json({ success: false, error: 'Missing priceId' }, { status: 400 });
    }

    const params = { // 确保 params 只在此处声明一次
      product_id: priceId,
      request_id: userId,
      success_url: `${appUrl}/payment/success`,
      // 根据 API 参考文档使用 customer 对象结构
      ...(creemCustomerId ? { customer: { id: creemCustomerId } } : (userEmail ? { customer: { email: userEmail } } : {})),
      metadata: {
        clerkUserId: userId,
        planType,            // 新增
      }
    };

    // --- 5. 调用 Creem API 创建 Checkout 会话 ---
    // 设置 Creem API 的 URL 和密钥。
    // 使用 fetch 发起 POST 请求到 Creem 的 /v1/checkouts 端点，发送准备好的参数。
    // 包含必要的请求头 (x-api-key, Content-Type, Accept)。
    const creemApiUrl = `${process.env.CREEM_API_URL}/v1/checkouts`;
    const creemSecretKey = process.env.CREEM_SECRET_KEY;
    // 添加日志验证
    console.log('CREEM_SECRET_KEY (first 10 chars):', creemSecretKey?.substring(0, 10));

    if (!creemSecretKey) {
        console.error("CRITICAL: CREEM_SECRET_KEY is not set inside POST handler.");
        // 注意：这里不应该直接返回 500 给前端，而是应该在配置层面解决
    }

    console.log("Creating Creem checkout session with params:", JSON.stringify(params));

    const response = await fetch(creemApiUrl, {
      method: 'POST',
      headers: {
        'x-api-key': creemSecretKey,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(params)
    });

    // --- 6. 处理 Creem API 响应 ---
    // 解析 Creem API 返回的 JSON 数据。
    // 检查 HTTP 响应状态码，如果不是成功状态 (非 2xx)，则记录错误并抛出异常。
    // 验证响应数据中是否包含必需的 checkout_url 字段，如果没有则记录错误并抛出异常。
    const responseData: unknown = await response.json();

    if (!response.ok) {
      console.error("Creem API Error creating checkout:", { status: response.status, body: responseData });
      // 类型守卫或断言 responseData
      const typedResponseData = responseData as CreemErrorResponse; // 类型断言
      const errorMessage = typedResponseData?.error?.message || typedResponseData?.message || `Failed to create checkout session (HTTP ${response.status})`;
      throw new Error(errorMessage);
    }

    // 确保响应包含 checkout_url
    const successfulResponseData = responseData as { checkout_url?: string; [key: string]: any };
    if (!successfulResponseData.checkout_url) {
      console.error("Creem API response missing checkout URL:", responseData);
      throw new Error('Creem checkout session created but checkout_url is missing.');
    }
    console.log("Creem checkout session created successfully. URL:", successfulResponseData.checkout_url);

    // --- 7. 返回成功响应 ---
    // 如果一切顺利，将包含 checkout_url 的成功响应返回给前端。
    // 前端将使用此 URL 将用户重定向到 Creem 的支付页面。
    return NextResponse.json({ success: true, url: successfulResponseData.checkout_url });

  } catch (error: any) {
    // --- 8. 统一错误处理 ---
    // 捕获在 try 块中发生的任何错误（包括网络错误、API 错误、配置错误等）。
    // 记录详细错误信息。
    // 向前端返回 500 Internal Server Error，并附带错误消息。
    console.error("Error during Creem session creation logic:", error);
    return NextResponse.json({ success: false, error: error.message || 'Internal Server Error during session creation' }, { status: 500 });
  }
} 

/*
说明：此路由处理 POST 请求，通过 Clerk 对用户进行身份验证，检索用户信息，准备参数（包括 $4.9 套餐的特定 priceId 和关键的 client_reference_id），使用 fetch 调用 Creem API，并在成功时返回 sessionId。包含了错误处理和日志记录。请记得将 BASIC_PLAN_PRICE_ID 替换为您从 Creem 获取的实际 Price ID。
*/