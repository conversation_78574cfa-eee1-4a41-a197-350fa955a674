// 这个API路由的功能是： 为已订阅的付费用户生成Creem客户门户链接，用于管理他们的订阅服务（如查看账单、修改或取消订阅等） 。
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { fetch } from 'undici';

const CREEM_API_URL = process.env.CREEM_API_URL!; // Base URL

export async function POST(request: NextRequest) {
  // 1. 用户认证检查
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  // 2. 检查环境变量配置
  const creemSecretKey = process.env.CREEM_SECRET_KEY;
  if (!creemSecretKey) {
      console.error("CRITICAL: CREEM_SECRET_KEY is not set.");
      return NextResponse.json({ success: false, error: 'Server configuration error.' }, { status: 500 });
  }

  try {
    // 3. 查询用户信息
    const user = await prisma.user.findUnique({
      where: { clerkUserId: userId },
      select: { creemCustomerId: true, subscriptionTier: true }, // Ensure creemCustomerId is selected
    });

    // Check if user exists and has a customer ID and is not on free tier
    // 4. 检查用户订阅状态
    if (!user || !user.creemCustomerId || user.subscriptionTier === 'free') {
      console.warn(`User ${userId} attempted portal access without Creem ID or is free tier.`);
      return NextResponse.json({ success: false, error: 'Subscription management not available.' }, { status: 403 });
    }

    const customerId = user.creemCustomerId;
   // 5. 调用Creem API创建客户门户会话
    console.log(`Requesting Creem customer portal link for customer ID: ${customerId}`);
    // Endpoint confirmed from creemDoc.md
    const response = await fetch(`${CREEM_API_URL}/v1/customers/billing`, {
      method: 'POST',
      headers: {
        // Use x-api-key based on Docs
        'x-api-key': creemSecretKey, // <<< UPDATED AUTH HEADER 使用API密钥认证
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        customer_id: customerId, // 传递客户ID
      }),
    });

    // 6. 处理API响应
    const responseData = await response.json() as any;

    if (!response.ok) {
      console.error(`Creem API Error (${response.status}) creating portal session for customer ${customerId}:`, responseData);
       const errorMessage = responseData?.error?.message || responseData?.message || `Failed to create portal session (HTTP ${response.status})`;
      throw new Error(errorMessage);
    }
    // 7. 验证响应数据
    // Response field confirmed from creemDoc.md
    if (!responseData.customer_portal_link) {
        console.error(`Creem API response missing 'customer_portal_link' for customer ${customerId}:`, responseData);
        throw new Error("Could not retrieve customer portal link from Creem.");
    }

    const portalUrl = responseData.customer_portal_link;
    console.log(`Successfully created Creem portal link for customer ${customerId}.`);

    // 8. 返回门户URL给前端
    return NextResponse.json({ success: true, url: portalUrl });

  } catch (error: any) {
    // 9. 错误处理
    console.error(`Error creating Creem portal session for user ${userId}:`, error);
    return NextResponse.json({ success: false, error: error.message || 'Failed to create portal session.' }, { status: 500 });
  }
}

/*
### 关键流程说明：
1. 认证检查 ：使用Clerk验证用户身份
2. 配置检查 ：确保Creem API密钥已配置
3. 用户查询 ：从数据库获取用户的Creem客户ID和订阅层级
4. 权限验证 ：
   - 用户必须存在
   - 必须有Creem客户ID
   - 不能是免费用户
5. API调用 ：向Creem发送请求创建客户门户会话
6. 响应处理 ：检查HTTP状态码和响应数据
7. 数据验证 ：确保返回了有效的门户链接
8. 结果返回 ：将门户URL返回给前端用于重定向
9. 错误处理 ：捕获并记录所有可能的错误
### 安全考虑：
- 严格的权限检查
- 敏感信息(API密钥)从环境变量获取
- 详细的错误日志记录
- 输入验证和响应验证
这个API端点主要用于生成让用户管理订阅的门户链接，前端获取这个链接后可以重定向用户到Creem的客户门户页面。
*/