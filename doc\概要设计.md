# PDF to Quiz 工具网站 (pdftoquiz.org) - 概要设计 (基于现有项目架构)

## 1. 引言

本文档基于现有 Next.js 项目架构，为 `pdftoquiz.org` 项目提供概要设计方案。项目将复用现有的技术栈、组件结构和开发模式，专注于 PDF 转测验的核心功能实现。

**项目目标**: 在现有项目基础上，创建一个用户友好、高效的在线工具，能够将用户上传的PDF文档智能地转换为可编辑、可导出的测验。

## 2. 现有项目架构分析

### 2.1 技术栈复用
- **前端**: Next.js 14 (App Router) + React 18 + TypeScript
- **样式**: Tailwind CSS + Shadcn UI 组件库
- **状态管理**: React Context + Providers
- **认证**: NextAuth.js (已配置)
- **国际化**: next-intl (已配置)
- **支付**: Stripe (已集成)
- **部署**: Vercel/Docker 支持

### 2.2 目录结构复用策略

基于现有项目结构，PDF to Quiz 功能将按以下方式集成：

```
pdftoquiz/
├── app/                                    # Next.js App Router (复用现有)
│   ├── [locale]/                           # 国际化路由 (复用)
│   │   ├── (default)/                      # 前台路由组 (复用)
│   │   │   ├── pdf-to-quiz/                # 新增：PDF转测验工具页
│   │   │   ├── ai-quiz-generator/          # 新增：AI测验生成器页
│   │   │   ├── mcq-generator/              # 新增：选择题生成器页
│   │   │   ├── teachers/                   # 新增：教师专用页面
│   │   │   ├── students/                   # 新增：学生专用页面
│   │   │   └── page.tsx                    # 修改：首页集成PDF工具
│   │   └── layout.tsx                      # 复用：全局布局
│   ├── api/                                # API路由 (扩展现有)
│   │   ├── pdf/                            # 新增：PDF处理API
│   │   │   ├── upload/route.ts
│   │   │   └── process/route.ts
│   │   ├── quiz/                           # 新增：测验生成API
│   │   │   ├── generate/route.ts
│   │   │   └── export/route.ts
│   │   └── auth/[...nextauth]/             # 复用：认证API
│   └── layout.tsx                          # 复用：根布局
├── aisdk/                                  # 扩展：AI服务封装
│   ├── quiz-generator/                     # 新增：测验生成服务
│   └── provider/                           # 复用：AI提供者抽象
├── components/                             # 扩展现有组件
│   ├── blocks/                             # 复用：页面块组件
│   │   ├── quiz/                           # 新增：测验相关块
│   │   └── pdf-upload/                     # 新增：PDF上传块
│   ├── ui/                                 # 复用：Shadcn UI组件
│   ├── quiz/                               # 新增：测验专用组件
│   └── upload/                             # 新增：上传专用组件
├── contexts/                               # 扩展：上下文管理
│   └── QuizContext.tsx                     # 新增：测验状态管理
├── i18n/                                   # 复用：国际化
│   ├── messages/                           # 扩展：全局翻译
│   └── pages/quiz/                         # 新增：测验页面翻译
├── lib/                                    # 扩展：工具函数
│   ├── pdf-processor.ts                   # 新增：PDF处理工具
│   └── quiz-generator.ts                  # 新增：测验生成工具
├── models/                                 # 扩展：数据模型
│   ├── quiz.ts                            # 新增：测验数据模型
│   └── pdf.ts                             # 新增：PDF数据模型
├── services/                               # 扩展：业务逻辑
│   ├── quizService.ts                      # 新增：测验业务逻辑
│   └── pdfService.ts                       # 新增：PDF业务逻辑
└── types/                                  # 扩展：类型定义
    ├── quiz.d.ts                          # 新增：测验类型
    └── pdf.d.ts                           # 新增：PDF类型
```

## 3. 系统架构设计

### 3.1 基于现有架构的扩展

```mermaid
graph TD
    User[用户浏览器] -->|HTTPS| Vercel[Vercel CDN]
    Vercel --> NextApp[Next.js App Router]
    
    subgraph "前端层 (复用现有)"
        NextApp --> Layout[Layout组件]
        Layout --> Pages[页面组件]
        Pages --> Blocks[Blocks组件]
        Blocks --> UI[Shadcn UI组件]
    end
    
    subgraph "API层 (扩展现有)"
        NextApp --> APIRoutes[API Routes]
        APIRoutes --> PDFApi[PDF处理API]
        APIRoutes --> QuizApi[测验生成API]
        APIRoutes --> AuthApi[认证API - 复用]
    end
    
    subgraph "服务层 (新增)"
        PDFApi --> PDFService[PDF处理服务]
        QuizApi --> QuizService[测验生成服务]
        QuizService --> AIProvider[AI提供者 - 复用架构]
    end
    
    subgraph "数据层 (扩展现有)"
        PDFService --> Database[(数据库)]
        QuizService --> Database
        Database --> UserData[用户数据 - 复用]
        Database --> QuizData[测验数据 - 新增]
    end
```

### 3.2 核心功能模块设计

#### 3.2.1 PDF处理模块 (新增)
- **位置**: `aisdk/pdf-processor/`
- **功能**: PDF文本提取、预处理、验证
- **复用**: 现有的文件上传和错误处理机制

#### 3.2.2 AI测验生成模块 (扩展现有AI架构)
- **位置**: `aisdk/quiz-generator/`
- **功能**: 基于PDF内容生成测验题目
- **复用**: 现有的AI提供者抽象层

#### 3.2.3 用户管理模块 (复用现有)
- **位置**: 复用现有的 `auth/` 和用户管理
- **功能**: 用户认证、会话管理、权限控制
- **扩展**: 添加测验相关的用户数据

## 4. 页面路由设计

### 4.1 基于现有路由结构的扩展

```typescript
// app/[locale]/(default)/ 下的新增页面
app/[locale]/(default)/
├── page.tsx                    # 首页 - 修改为集成PDF工具
├── pdf-to-quiz/
│   └── page.tsx               # PDF转测验工具页
├── ai-quiz-generator/
│   └── page.tsx               # AI测验生成器页
├── mcq-generator/
│   └── page.tsx               # 选择题生成器页
├── teachers/
│   └── page.tsx               # 教师专用页面
├── students/
│   └── page.tsx               # 学生专用页面
└── about/                     # 复用现有关于页面
    └── page.tsx
```

### 4.2 API路由扩展

```typescript
// app/api/ 下的新增API
app/api/
├── pdf/
│   ├── upload/route.ts        # PDF文件上传
│   ├── process/route.ts       # PDF文本提取
│   └── validate/route.ts      # PDF文件验证
├── quiz/
│   ├── generate/route.ts      # 测验生成
│   ├── export/route.ts        # 测验导出
│   └── save/route.ts          # 测验保存 (P1)
├── auth/[...nextauth]/        # 复用现有认证
└── stripe-notify/             # 复用现有支付 (P2)
```

## 5. 组件设计

### 5.1 复用现有组件架构

#### 5.1.1 Blocks组件 (扩展现有)
```typescript
// components/blocks/ 下新增测验相关块
components/blocks/
├── hero/                      # 复用现有Hero组件
├── feature/                   # 复用现有Feature组件
├── quiz/                      # 新增：测验相关块
│   ├── QuizHero.tsx
│   ├── QuizFeatures.tsx
│   └── QuizTestimonials.tsx
├── pdf-upload/                # 新增：PDF上传块
│   ├── UploadHero.tsx
│   └── UploadInstructions.tsx
└── cta/                       # 复用现有CTA组件
```

#### 5.1.2 UI组件 (复用Shadcn UI)
```typescript
// components/ui/ 复用现有组件
components/ui/
├── button.tsx                 # 复用
├── input.tsx                  # 复用
├── card.tsx                   # 复用
├── modal.tsx                  # 复用
├── progress.tsx               # 复用
└── toast.tsx                  # 复用 (sonner)
```

#### 5.1.3 新增专用组件
```typescript
// 新增测验和上传专用组件
components/
├── quiz/
│   ├── QuestionCard.tsx
│   ├── QuizResults.tsx
│   ├── GenerationProgress.tsx
│   └── ExportOptions.tsx
└── upload/
    ├── FileUploader.tsx
    ├── UploadProgress.tsx
    └── ConfigurationModal.tsx
```

### 5.2 状态管理 (扩展现有Context)

```typescript
// contexts/QuizContext.tsx - 新增
interface QuizContextType {
  currentQuiz: Quiz | null;
  uploadedFile: UploadedFile | null;
  generationStatus: GenerationStatus;
  setCurrentQuiz: (quiz: Quiz) => void;
  setUploadedFile: (file: UploadedFile) => void;
  updateGenerationStatus: (status: GenerationStatus) => void;
}

// 复用现有的Provider模式
// providers/QuizProvider.tsx
export const QuizProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 状态管理逻辑
  return (
    <QuizContext.Provider value={contextValue}>
      {children}
    </QuizContext.Provider>
  );
};
```

## 6. 数据模型设计

### 6.1 扩展现有数据模型

```typescript
// models/quiz.ts - 新增
export interface Quiz {
  id: string;
  userId?: string;           // 关联现有用户系统
  title: string;
  questions: Question[];
  createdAt: Date;
  updatedAt: Date;
  sourceFile?: {
    name: string;
    size: number;
    pageCount: number;
  };
  config: QuizConfig;
}

// models/pdf.ts - 新增
export interface PDFDocument {
  id: string;
  userId?: string;           // 关联现有用户系统
  filename: string;
  size: number;
  pageCount: number;
  extractedText: string;
  status: 'uploaded' | 'processing' | 'processed' | 'failed';
  uploadedAt: Date;
}

// 复用现有的用户模型
// models/user.ts (扩展)
export interface User {
  // 现有字段...
  quizzes?: Quiz[];          // 新增：用户的测验列表
  uploadHistory?: PDFDocument[]; // 新增：上传历史
}
```

### 6.2 数据库设计 (扩展现有)

```sql
-- 基于现有数据库架构扩展
-- 测验表
CREATE TABLE quizzes (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id), -- 关联现有用户表
  title VARCHAR(255),
  questions JSONB NOT NULL,
  config JSONB,
  source_file_name VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- PDF文档表
CREATE TABLE pdf_documents (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id), -- 关联现有用户表
  filename VARCHAR(255) NOT NULL,
  file_size INTEGER,
  page_count INTEGER,
  extracted_text TEXT,
  status VARCHAR(20) DEFAULT 'uploaded',
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 7. 服务层设计

### 7.1 复用现有服务架构

```typescript
// services/quizService.ts - 新增
export class QuizService {
  constructor(
    private aiProvider: AIProvider,    // 复用现有AI提供者
    private userService: UserService  // 复用现有用户服务
  ) {}

  async generateQuiz(pdfText: string, config: QuizConfig): Promise<Quiz> {
    // 测验生成逻辑
  }

  async saveQuiz(quiz: Quiz, userId?: string): Promise<string> {
    // 保存测验逻辑，集成现有用户系统
  }
}

// services/pdfService.ts - 新增
export class PDFService {
  async processPDF(file: File): Promise<PDFDocument> {
    // PDF处理逻辑
  }

  async extractText(buffer: Buffer): Promise<string> {
    // 文本提取逻辑
  }
}
```

### 7.2 AI服务集成 (扩展现有)

```typescript
// aisdk/quiz-generator/ - 新增
export class QuizGeneratorProvider implements AIProvider {
  async generateQuestions(
    text: string, 
    config: QuizConfig
  ): Promise<Question[]> {
    // 复用现有的AI调用模式
    return this.callAI({
      model: 'gpt-3.5-turbo',
      prompt: this.buildPrompt(text, config),
      // 其他配置...
    });
  }
}
```

## 8. 国际化集成

### 8.1 复用现有i18n架构

```json
// i18n/messages/en.json (扩展现有)
{
  // 现有翻译...
  "quiz": {
    "title": "PDF to Quiz Converter",
    "upload": {
      "title": "Upload your PDF",
      "description": "Drag and drop or click to select",
      "limits": "PDF files only • Up to 5MB • Max 50 pages"
    },
    "generation": {
      "analyzing": "Analyzing your PDF...",
      "generating": "Generating quiz questions...",
      "completed": "Quiz generated successfully!"
    }
  }
}

// i18n/pages/quiz/en.json (新增)
{
  "hero": {
    "title": "Turn Any PDF into a Quiz in Seconds",
    "subtitle": "Upload your PDF documents and let AI generate high-quality multiple choice questions instantly."
  }
}
```

## 9. SEO架构设计

### 9.1 基于现有SEO模式扩展

```typescript
// 复用现有的SEO组件和模式
// app/[locale]/(default)/pdf-to-quiz/page.tsx
export const metadata: Metadata = {
  title: 'PDF to Quiz Converter - Transform PDFs into Interactive Quizzes',
  description: 'Convert any PDF into engaging quizzes automatically...',
  // 复用现有的metadata模式
};

// 复用现有的sitemap生成逻辑
// 在现有sitemap中添加新页面
const quizPages = [
  '/pdf-to-quiz',
  '/ai-quiz-generator', 
  '/mcq-generator',
  '/teachers',
  '/students'
];
```

## 10. 部署和配置

### 10.1 复用现有部署配置

```typescript
// next.config.mjs (扩展现有配置)
const nextConfig = {
  // 现有配置...
  
  // 新增PDF处理相关配置
  experimental: {
    serverComponentsExternalPackages: ['pdf-parse']
  },
  
  // 文件上传限制
  api: {
    bodyParser: {
      sizeLimit: '5mb',
    },
  }
};
```

```dockerfile
# Dockerfile (扩展现有)
# 现有配置...

# 新增PDF处理依赖
RUN apt-get update && apt-get install -y \
  poppler-utils \
  && rm -rf /var/lib/apt/lists/*
```

### 10.2 环境变量扩展

```bash
# .env.local (扩展现有)
# 现有环境变量...

# PDF处理配置
PDF_MAX_SIZE=5242880  # 5MB
PDF_MAX_PAGES=50

# AI服务配置 (复用现有)
OPENAI_API_KEY=your_openai_key
```

## 11. 开发计划

### 11.1 P0阶段 (MVP - 4周)
- **Week 1**: 基础架构搭建，复用现有组件
- **Week 2**: PDF上传和处理功能
- **Week 3**: AI测验生成核心功能
- **Week 4**: 首页集成和基础导出功能

### 11.2 P1阶段 (功能完善 - 6周)
- 用户账户集成 (复用现有认证)
- 测验编辑功能
- PDF导出功能
- 历史记录管理

### 11.3 P2阶段 (高级功能 - 8周)
- OCR支持
- 多语言测验生成
- 高级导出格式
- 付费功能集成 (复用现有Stripe)

## 12. 总结

本概要设计充分复用了现有项目的技术架构、组件体系和开发模式，通过扩展而非重构的方式实现PDF转测验功能。这种方式能够：

1. **降低开发成本**: 复用现有的UI组件、认证系统、国际化配置
2. **保持一致性**: 与现有项目的设计语言和用户体验保持一致
3. **快速上线**: 基于成熟的架构快速实现核心功能
4. **易于维护**: 遵循现有的代码组织和开发规范

后续的详细设计将在此基础上进一步细化各个模块的具体实现。
