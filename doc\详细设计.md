# PDF to Quiz 工具网站 (pdftoquiz.org) - 详细设计文档 (基于现有项目架构)

## 1. 引言

本文档基于现有 Next.js 项目架构和《概要设计 (基于现有项目架构)》，为 `pdftoquiz.org` 项目提供详细的技术实现方案。项目将充分复用现有的组件体系、技术栈和开发模式。

**设计目标**: 在现有项目基础上，实现功能集中在首页、操作简单直观、强调教育公益属性的 PDF 转测验工具。

## 2. 项目结构详细设计

### 2.1 基于现有架构的目录结构

```
pdftoquiz/
├── app/                                    # Next.js App Router (复用现有)
│   ├── [locale]/                           # 国际化路由 (复用)
│   │   ├── (default)/                      # 前台路由组 (复用)
│   │   │   ├── page.tsx                    # 首页 - 集成PDF工具
│   │   │   ├── pdf-to-quiz/
│   │   │   │   └── page.tsx               # PDF转测验专用页
│   │   │   ├── ai-quiz-generator/
│   │   │   │   └── page.tsx               # AI测验生成器页
│   │   │   ├── mcq-generator/
│   │   │   │   └── page.tsx               # 选择题生成器页
│   │   │   ├── teachers/
│   │   │   │   └── page.tsx               # 教师专用页面
│   │   │   ├── students/
│   │   │   │   └── page.tsx               # 学生专用页面
│   │   │   ├── about/                      # 复用现有
│   │   │   └── posts/                      # 复用现有博客
│   │   ├── (console)/                      # 复用现有用户控制台
│   │   │   ├── my-quizzes/                 # 新增：我的测验
│   │   │   │   └── page.tsx
│   │   │   ├── my-credits/                 # 复用现有积分系统
│   │   │   └── my-orders/                  # 复用现有订单系统
│   │   └── layout.tsx                      # 复用全局布局
│   ├── api/                                # API路由 (扩展现有)
│   │   ├── pdf/
│   │   │   ├── upload/route.ts             # PDF上传处理
│   │   │   ├── process/route.ts            # PDF文本提取
│   │   │   └── validate/route.ts           # PDF验证
│   │   ├── quiz/
│   │   │   ├── generate/route.ts           # 测验生成
│   │   │   ├── export/route.ts             # 测验导出
│   │   │   └── save/route.ts               # 测验保存
│   │   ├── auth/[...nextauth]/             # 复用现有认证
│   │   ├── checkout/                       # 复用现有支付
│   │   └── get-user-info/                  # 复用现有用户信息
│   └── layout.tsx                          # 复用根布局
├── aisdk/                                  # 扩展现有AI服务
│   ├── quiz-generator/                     # 新增测验生成
│   │   ├── index.ts
│   │   ├── prompts.ts
│   │   └── types.ts
│   ├── pdf-processor/                      # 新增PDF处理
│   │   ├── index.ts
│   │   ├── extractor.ts
│   │   └── validator.ts
│   └── provider/                           # 复用现有AI提供者
├── components/                             # 扩展现有组件
│   ├── blocks/                             # 复用现有页面块
│   │   ├── hero/                           # 复用现有Hero
│   │   ├── feature/                        # 复用现有Feature
│   │   ├── cta/                            # 复用现有CTA
│   │   ├── quiz/                           # 新增测验相关块
│   │   │   ├── QuizHero.tsx
│   │   │   ├── QuizFeatures.tsx
│   │   │   ├── QuizTestimonials.tsx
│   │   │   └── HowItWorks.tsx
│   │   └── pdf-upload/                     # 新增PDF上传块
│   │       ├── UploadSection.tsx
│   │       └── UploadInstructions.tsx
│   ├── ui/                                 # 复用Shadcn UI组件
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── input.tsx
│   │   ├── progress.tsx
│   │   ├── dialog.tsx
│   │   └── toast.tsx
│   ├── quiz/                               # 新增测验专用组件
│   │   ├── QuestionCard.tsx
│   │   ├── QuizResults.tsx
│   │   ├── GenerationProgress.tsx
│   │   ├── ExportOptions.tsx
│   │   └── ConfigurationModal.tsx
│   ├── upload/                             # 新增上传专用组件
│   │   ├── FileUploader.tsx
│   │   ├── UploadProgress.tsx
│   │   └── FileValidator.tsx
│   ├── console/                            # 复用现有控制台组件
│   └── dashboard/                          # 复用现有仪表盘组件
├── contexts/                               # 扩展现有上下文
│   ├── AppContext.tsx                      # 复用现有应用上下文
│   └── QuizContext.tsx                     # 新增测验上下文
├── i18n/                                   # 复用现有国际化
│   ├── messages/                           # 扩展全局翻译
│   │   ├── en.json                         # 扩展英文翻译
│   │   └── zh.json                         # 扩展中文翻译
│   └── pages/                              # 扩展页面翻译
│       ├── landing/                        # 复用现有首页翻译
│       └── quiz/                           # 新增测验页面翻译
│           ├── en.json
│           └── zh.json
├── lib/                                    # 扩展现有工具函数
│   ├── utils.ts                            # 复用现有工具函数
│   ├── pdf-processor.ts                   # 新增PDF处理工具
│   ├── quiz-generator.ts                  # 新增测验生成工具
│   └── file-validator.ts                  # 新增文件验证工具
├── models/                                 # 扩展现有数据模型
│   ├── user.ts                            # 复用现有用户模型
│   ├── quiz.ts                            # 新增测验数据模型
│   └── pdf.ts                             # 新增PDF数据模型
├── providers/                              # 复用现有提供者
│   ├── ThemeProvider.tsx                   # 复用主题提供者
│   ├── SessionProvider.tsx                 # 复用会话提供者
│   └── QuizProvider.tsx                    # 新增测验提供者
├── services/                               # 扩展现有服务
│   ├── quizService.ts                      # 新增测验业务逻辑
│   ├── pdfService.ts                       # 新增PDF业务逻辑
│   └── userService.ts                      # 复用现有用户服务
└── types/                                  # 扩展现有类型定义
    ├── index.d.ts                          # 复用现有全局类型
    ├── quiz.d.ts                           # 新增测验类型
    └── pdf.d.ts                            # 新增PDF类型
```

## 3. 页面设计详细实现

### 3.1 首页设计 (复用现有Layout)

```typescript
// app/[locale]/(default)/page.tsx
import { Metadata } from 'next';
import { useTranslations } from 'next-intl';
import { HeroSection } from '@/components/blocks/hero/HeroSection';
import { QuizHero } from '@/components/blocks/quiz/QuizHero';
import { QuizFeatures } from '@/components/blocks/quiz/QuizFeatures';
import { HowItWorks } from '@/components/blocks/quiz/HowItWorks';
import { QuizTestimonials } from '@/components/blocks/quiz/QuizTestimonials';
import { CTASection } from '@/components/blocks/cta/CTASection';

export const metadata: Metadata = {
  title: 'PDF to Quiz Converter - Free AI Tool | pdftoquiz.org',
  description: 'Convert any PDF into engaging quizzes automatically. Upload your PDF and generate multiple choice questions in minutes. 100% free tool with no registration required.',
  keywords: 'pdf to quiz, ai quiz generator, pdf quiz converter, free quiz maker',
};

export default function HomePage() {
  const t = useTranslations('landing');

  return (
    <div className="min-h-screen">
      {/* 复用现有Hero结构，内容改为PDF工具 */}
      <QuizHero />
      
      {/* 操作流程说明 */}
      <HowItWorks />
      
      {/* 功能特性 - 复用现有Feature组件结构 */}
      <QuizFeatures />
      
      {/* 用户证言 - 复用现有Testimonial组件结构 */}
      <QuizTestimonials />
      
      {/* CTA区域 - 复用现有CTA组件 */}
      <CTASection />
    </div>
  );
}
```

### 3.2 PDF转测验专用页面

```typescript
// app/[locale]/(default)/pdf-to-quiz/page.tsx
import { Metadata } from 'next';
import { QuizProvider } from '@/providers/QuizProvider';
import { FileUploader } from '@/components/upload/FileUploader';
import { QuizResults } from '@/components/quiz/QuizResults';
import { Breadcrumb } from '@/components/ui/breadcrumb';

export const metadata: Metadata = {
  title: 'PDF to Quiz Converter - Transform PDFs into Interactive Quizzes',
  description: 'Convert PDF documents into interactive quizzes using AI. Free, fast, and easy to use.',
  canonical: 'https://pdftoquiz.org/pdf-to-quiz',
};

export default function PdfToQuizPage() {
  return (
    <QuizProvider>
      <div className="container mx-auto px-4 py-8">
        {/* 面包屑导航 - 复用现有组件模式 */}
        <Breadcrumb 
          items={[
            { label: 'Home', href: '/' },
            { label: 'PDF to Quiz Converter' }
          ]} 
        />
        
        {/* 页面标题 */}
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Convert PDF to Quiz in Seconds
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transform any PDF document into engaging multiple choice questions using our AI-powered quiz generator.
          </p>
        </header>

        {/* 主要功能区域 */}
        <div className="max-w-4xl mx-auto">
          <FileUploader />
          <QuizResults />
        </div>
      </div>
    </QuizProvider>
  );
}
```

## 4. 组件详细实现

### 4.1 核心上传组件 (基于现有UI组件)

```typescript
// components/upload/FileUploader.tsx
'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner'; // 复用现有toast
import { useQuiz } from '@/contexts/QuizContext';

interface FileUploaderProps {
  className?: string;
}

export const FileUploader: React.FC<FileUploaderProps> = ({ className }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { setUploadedFile, setGenerationStatus } = useQuiz();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    
    if (!file) return;
    
    // 文件验证
    if (file.type !== 'application/pdf') {
      toast.error('Please upload a PDF file only');
      return;
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('File size must be less than 5MB');
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // 上传文件
      const formData = new FormData();
      formData.append('pdf', file);
      
      const response = await fetch('/api/pdf/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Upload failed');
      }
      
      const result = await response.json();
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      // 更新上下文状态
      setUploadedFile({
        id: result.fileId,
        name: file.name,
        size: file.size,
        pageCount: result.pageCount
      });
      
      toast.success('PDF uploaded successfully!');
      
    } catch (error) {
      toast.error('Upload failed. Please try again.');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  }, [setUploadedFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxFiles: 1,
    disabled: isUploading
  });

  return (
    <Card className={`p-8 ${className}`}>
      <div 
        {...getRootProps()} 
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'}
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        {isUploading ? (
          <div className="space-y-4">
            <div className="text-4xl">⏳</div>
            <h3 className="text-lg font-semibold">Uploading...</h3>
            <Progress value={uploadProgress} className="max-w-xs mx-auto" />
            <p className="text-sm text-gray-600">{uploadProgress}% complete</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-4xl">📁⬆️</div>
            <h3 className="text-lg font-semibold">
              {isDragActive ? 'Drop your PDF here' : 'Click to upload or drag and drop'}
            </h3>
            <p className="text-gray-500">
              PDF files only • Up to 5MB • Max 50 pages
            </p>
            <Button size="lg" className="mt-4">
              Choose PDF File
            </Button>
          </div>
        )}
      </div>
      
      <p className="text-sm text-gray-500 text-center mt-4">
        🔒 Your files are processed securely and deleted after quiz generation
      </p>
    </Card>
  );
};
```

### 4.2 测验结果组件

```typescript
// components/quiz/QuizResults.tsx
'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QuestionCard } from './QuestionCard';
import { ExportOptions } from './ExportOptions';
import { useQuiz } from '@/contexts/QuizContext';

export const QuizResults: React.FC = () => {
  const { currentQuiz, generationStatus } = useQuiz();

  if (!currentQuiz) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* 结果标题 */}
      <Card className="p-6 text-center bg-green-50 border-green-200">
        <h2 className="text-2xl font-bold text-green-700 mb-2">
          🎉 Quiz Generated Successfully!
        </h2>
        <p className="text-green-600">
          Generated {currentQuiz.questions.length} questions from your PDF
        </p>
      </Card>

      {/* 导出选项 */}
      <ExportOptions quiz={currentQuiz} />

      {/* 题目列表 */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Generated Questions</h3>
        {currentQuiz.questions.map((question, index) => (
          <QuestionCard 
            key={question.id}
            question={question}
            questionNumber={index + 1}
          />
        ))}
      </div>

      {/* 底部提示 */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <p className="text-sm text-blue-700 text-center">
          💡 <strong>Tip:</strong> Create an account to save your quizzes and unlock editing features!
        </p>
      </Card>
    </div>
  );
};
```

### 4.3 题目卡片组件

```typescript
// components/quiz/QuestionCard.tsx
'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Question } from '@/types/quiz';

interface QuestionCardProps {
  question: Question;
  questionNumber: number;
  showEdit?: boolean;
  onEdit?: (questionId: string) => void;
}

export const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  questionNumber,
  showEdit = false,
  onEdit
}) => {
  const correctOption = question.options.find(opt => opt.isCorrect);

  return (
    <Card className="p-6">
      {/* 题目标题 */}
      <div className="flex justify-between items-start mb-4">
        <h4 className="text-lg font-semibold">Question {questionNumber}</h4>
        {showEdit && onEdit && (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => onEdit(question.id)}
          >
            ✏️ Edit
          </Button>
        )}
      </div>
      
      {/* 题目内容 */}
      <div className="space-y-4">
        <p className="text-gray-800 font-medium">
          {question.text}
        </p>
        
        {/* 选项 */}
        <div className="space-y-2">
          {question.options.map((option, index) => (
            <div 
              key={option.id}
              className={`
                flex items-center p-3 border rounded-md
                ${option.isCorrect 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-gray-200'
                }
              `}
            >
              <span className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mr-3
                ${option.isCorrect ? 'bg-green-100' : 'bg-gray-100'}
              `}>
                {String.fromCharCode(65 + index)} {/* A, B, C, D */}
              </span>
              <span className="flex-1">{option.text}</span>
              {option.isCorrect && (
                <span className="text-green-600 font-medium">
                  ✓ Correct
                </span>
              )}
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};
```

## 5. API路由实现

### 5.1 PDF上传API

```typescript
// app/api/pdf/upload/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth'; // 复用现有认证配置
import { PDFService } from '@/services/pdfService';

export async function POST(request: NextRequest) {
  try {
    // 可选：检查用户认证状态
    const session = await getServerSession(authOptions);
    
    const formData = await request.formData();
    const file = formData.get('pdf') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    // 文件验证
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'Only PDF files are allowed' },
        { status: 400 }
      );
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB
      return NextResponse.json(
        { error: 'File size exceeds 5MB limit' },
        { status: 400 }
      );
    }
    
    // 处理PDF
    const pdfService = new PDFService();
    const buffer = Buffer.from(await file.arrayBuffer());
    const result = await pdfService.processPDF(buffer, file.name, session?.user?.id);
    
    return NextResponse.json({
      success: true,
      fileId: result.id,
      filename: result.filename,
      pageCount: result.pageCount,
      extractedText: result.extractedText.substring(0, 500) + '...' // 预览
    });
    
  } catch (error) {
    console.error('PDF upload error:', error);
    return NextResponse.json(
      { error: 'Failed to process PDF' },
      { status: 500 }
    );
  }
}
```

### 5.2 测验生成API

```typescript
// app/api/quiz/generate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { QuizService } from '@/services/quizService';
import { PDFService } from '@/services/pdfService';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { fileId, config } = await request.json();
    
    if (!fileId || !config) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    // 获取PDF文本
    const pdfService = new PDFService();
    const pdfDocument = await pdfService.getPDFById(fileId);
    
    if (!pdfDocument) {
      return NextResponse.json(
        { error: 'PDF not found' },
        { status: 404 }
      );
    }
    
    // 生成测验
    const quizService = new QuizService();
    const quiz = await quizService.generateQuiz(
      pdfDocument.extractedText,
      config,
      session?.user?.id
    );
    
    return NextResponse.json({
      success: true,
      quiz: quiz
    });
    
  } catch (error) {
    console.error('Quiz generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate quiz' },
      { status: 500 }
    );
  }
}
```

## 6. 服务层实现

### 6.1 PDF处理服务

```typescript
// services/pdfService.ts
import * as pdfParse from 'pdf-parse';
import { PDFDocument } from '@/types/pdf';

export class PDFService {
  async processPDF(buffer: Buffer, filename: string, userId?: string): Promise<PDFDocument> {
    try {
      // 使用pdf-parse提取文本
      const pdfData = await pdfParse(buffer);
      
      // 验证页数限制
      if (pdfData.numpages > 50) {
        throw new Error('PDF exceeds 50 pages limit');
      }
      
      // 验证文本内容
      if (pdfData.text.length < 100) {
        throw new Error('PDF contains insufficient text content');
      }
      
      // 文本预处理
      const cleanedText = this.preprocessText(pdfData.text);
      
      // 创建PDF文档记录
      const pdfDocument: PDFDocument = {
        id: this.generateId(),
        userId,
        filename,
        size: buffer.length,
        pageCount: pdfData.numpages,
        extractedText: cleanedText,
        status: 'processed',
        uploadedAt: new Date()
      };
      
      // 这里可以选择保存到数据库或仅返回
      // await this.savePDFDocument(pdfDocument);
      
      return pdfDocument;
      
    } catch (error) {
      throw new Error(`PDF processing failed: ${error.message}`);
    }
  }
  
  private preprocessText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\n{3,}/g, '\n\n') // 限制换行符数量
      .trim();
  }
  
  private generateId(): string {
    return `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  async getPDFById(id: string): Promise<PDFDocument | null> {
    // 这里实现从数据库或缓存获取PDF文档
    // 暂时返回null，实际实现时需要连接数据库
    return null;
  }
}
```

### 6.2 测验生成服务

```typescript
// services/quizService.ts
import { Quiz, Question, QuizConfig } from '@/types/quiz';
import { QuizGeneratorProvider } from '@/aisdk/quiz-generator';

export class QuizService {
  private quizGenerator: QuizGeneratorProvider;
  
  constructor() {
    this.quizGenerator = new QuizGeneratorProvider();
  }
  
  async generateQuiz(
    text: string, 
    config: QuizConfig, 
    userId?: string
  ): Promise<Quiz> {
    try {
      // 使用AI生成题目
      const questions = await this.quizGenerator.generateQuestions(text, config);
      
      // 创建测验对象
      const quiz: Quiz = {
        id: this.generateId(),
        userId,
        title: `Quiz from PDF - ${new Date().toLocaleDateString()}`,
        questions,
        createdAt: new Date(),
        updatedAt: new Date(),
        config
      };
      
      // 可选：保存到数据库
      if (userId) {
        await this.saveQuiz(quiz);
      }
      
      return quiz;
      
    } catch (error) {
      throw new Error(`Quiz generation failed: ${error.message}`);
    }
  }
  
  private generateId(): string {
    return `quiz_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private async saveQuiz(quiz: Quiz): Promise<void> {
    // 实现保存到数据库的逻辑
    // 这里暂时为空，实际实现时需要连接数据库
  }
}
```

## 7. AI服务实现

### 7.1 测验生成AI提供者

```typescript
// aisdk/quiz-generator/index.ts
import { OpenAI } from 'openai';
import { Question, QuizConfig } from '@/types/quiz';
import { buildMCQPrompt } from './prompts';

export class QuizGeneratorProvider {
  private openai: OpenAI;
  
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }
  
  async generateQuestions(text: string, config: QuizConfig): Promise<Question[]> {
    try {
      const prompt = buildMCQPrompt(text, config.numQuestions);
      
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert quiz creator. Generate high-quality multiple choice questions based on the provided text.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      });
      
      const generatedContent = response.choices[0].message.content;
      return this.parseQuestionResponse(generatedContent);
      
    } catch (error) {
      throw new Error(`AI quiz generation failed: ${error.message}`);
    }
  }
  
  private parseQuestionResponse(response: string): Question[] {
    try {
      const parsed = JSON.parse(response);
      return parsed.questions.map((q: any, index: number) => ({
        id: `q_${Date.now()}_${index}`,
        text: q.question,
        type: 'mcq' as const,
        options: q.options.map((opt: any, optIndex: number) => ({
          id: `opt_${Date.now()}_${index}_${optIndex}`,
          text: opt.text,
          isCorrect: opt.correct
        }))
      }));
    } catch (error) {
      throw new Error('Failed to parse AI response');
    }
  }
}
```

### 7.2 AI提示词模板

```typescript
// aisdk/quiz-generator/prompts.ts
export function buildMCQPrompt(text: string, numQuestions: number): string {
  return `
Based on the following text, generate ${numQuestions} multiple choice questions (MCQ). 

Requirements:
- Each question should have 4 options (A, B, C, D)
- Only one option should be correct
- Questions should test understanding, not just memorization
- Avoid overly obvious or tricky questions
- Return the response in valid JSON format

Text to analyze:
${text.substring(0, 3000)}

Required JSON format:
{
  "questions": [
    {
      "question": "Question text here?",
      "options": [
        {"text": "Option A", "correct": false},
        {"text": "Option B", "correct": true},
        {"text": "Option C", "correct": false},
        {"text": "Option D", "correct": false}
      ]
    }
  ]
}
  `.trim();
}
```

## 8. 类型定义

### 8.1 测验相关类型

```typescript
// types/quiz.d.ts
export interface Quiz {
  id: string;
  userId?: string;
  title: string;
  questions: Question[];
  createdAt: Date;
  updatedAt: Date;
  sourceFile?: {
    name: string;
    size: number;
    pageCount: number;
  };
  config: QuizConfig;
}

export interface Question {
  id: string;
  text: string;
  type: 'mcq' | 'true-false';
  options: Option[];
  sourceReference?: {
    page: number;
    paragraph: number;
    text: string;
  };
}

export interface Option {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface QuizConfig {
  numQuestions: number;
  questionType: 'mcq' | 'true-false';
  difficulty?: 'easy' | 'medium' | 'hard';
  language?: string;
}

export interface GenerationStatus {
  status: 'idle' | 'uploading' | 'processing' | 'generating' | 'completed' | 'error';
  progress: number;
  message?: string;
  error?: string;
}
```

### 8.2 PDF相关类型

```typescript
// types/pdf.d.ts
export interface PDFDocument {
  id: string;
  userId?: string;
  filename: string;
  size: number;
  pageCount: number;
  extractedText: string;
  status: 'uploaded' | 'processing' | 'processed' | 'failed';
  uploadedAt: Date;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  pageCount?: number;
}
```

## 9. 状态管理

### 9.1 测验上下文

```typescript
// contexts/QuizContext.tsx
'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Quiz, GenerationStatus, UploadedFile } from '@/types';

interface QuizContextType {
  currentQuiz: Quiz | null;
  uploadedFile: UploadedFile | null;
  generationStatus: GenerationStatus;
  setCurrentQuiz: (quiz: Quiz | null) => void;
  setUploadedFile: (file: UploadedFile | null) => void;
  setGenerationStatus: (status: GenerationStatus) => void;
  updateGenerationStatus: (updates: Partial<GenerationStatus>) => void;
}

const QuizContext = createContext<QuizContextType | undefined>(undefined);

export const useQuiz = () => {
  const context = useContext(QuizContext);
  if (context === undefined) {
    throw new Error('useQuiz must be used within a QuizProvider');
  }
  return context;
};

interface QuizProviderProps {
  children: ReactNode;
}

export const QuizProvider: React.FC<QuizProviderProps> = ({ children }) => {
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus>({
    status: 'idle',
    progress: 0
  });

  const updateGenerationStatus = (updates: Partial<GenerationStatus>) => {
    setGenerationStatus(prev => ({ ...prev, ...updates }));
  };

  const value: QuizContextType = {
    currentQuiz,
    uploadedFile,
    generationStatus,
    setCurrentQuiz,
    setUploadedFile,
    setGenerationStatus,
    updateGenerationStatus
  };

  return (
    <QuizContext.Provider value={value}>
      {children}
    </QuizContext.Provider>
  );
};
```

## 10. 国际化扩展

### 10.1 测验相关翻译

```json
// i18n/messages/en.json (扩展现有)
{
  "quiz": {
    "title": "PDF to Quiz Converter",
    "subtitle": "Transform any PDF into engaging quizzes automatically",
    "upload": {
      "title": "Upload your PDF",
      "description": "Drag and drop or click to select",
      "limits": "PDF files only • Up to 5MB • Max 50 pages",
      "button": "Choose PDF File",
      "uploading": "Uploading...",
      "success": "PDF uploaded successfully!",
      "error": "Upload failed. Please try again."
    },
    "generation": {
      "analyzing": "Analyzing your PDF...",
      "generating": "Generating quiz questions...",
      "completed": "Quiz generated successfully!",
      "error": "Failed to generate quiz"
    },
    "results": {
      "title": "Quiz Generated Successfully!",
      "subtitle": "Generated {count} questions from your PDF",
      "export": "Export Options",
      "copy": "Copy as Text",
      "download": "Download TXT"
    }
  }
}
```

```json
// i18n/messages/zh.json (扩展现有)
{
  "quiz": {
    "title": "PDF转测验工具",
    "subtitle": "自动将任何PDF转换为引人入胜的测验",
    "upload": {
      "title": "上传您的PDF",
      "description": "拖拽文件或点击选择",
      "limits": "仅支持PDF文件 • 最大5MB • 最多50页",
      "button": "选择PDF文件",
      "uploading": "上传中...",
      "success": "PDF上传成功！",
      "error": "上传失败，请重试"
### 8.6 数据库设计补充

#### 8.6.1 SEO相关数据表

```sql
-- SEO页面元数据表
CREATE TABLE seo_metadata (
  id SERIAL PRIMARY KEY,
  page_path VARCHAR(255) UNIQUE NOT NULL,
  title VARCHAR(255),
  meta_description TEXT,
  keywords TEXT[],
  canonical_url VARCHAR(255),
  og_title VARCHAR(255),
  og_description TEXT,
  og_image VARCHAR(255),
  json_ld JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 博客文章表
CREATE TABLE blog_posts (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  excerpt TEXT,
  content TEXT NOT NULL,
  featured_image VARCHAR(255),
  category_id INTEGER REFERENCES blog_categories(id),
  seo_title VARCHAR(255),
  meta_description TEXT,
  keywords TEXT[],
  published_at TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  reading_time INTEGER, -- 预计阅读时间（分钟）
  view_count INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft' -- draft, published, archived
);

-- 博客分类表
CREATE TABLE blog_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  seo_title VARCHAR(255),
  meta_description TEXT
);

-- 关键词排名监控表
CREATE TABLE keyword_rankings (
  id SERIAL PRIMARY KEY,
  keyword VARCHAR(255) NOT NULL,
  page_url VARCHAR(255) NOT NULL,
  search_engine VARCHAR(50) NOT NULL, -- google, bing, etc.
  ranking_position INTEGER,
  search_volume INTEGER,
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_keyword_date (keyword, recorded_at)
);
```

### 8.7 组件结构更新

根据SEO需求，更新组件结构如下：

```typescript
// 更新后的组件层次结构
src/
├── components/
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   ├── Layout.tsx
│   │   └── SEOOptimizedLayout.tsx           // 新增
│   ├── seo/                                 // 新增SEO组件目录
│   │   ├── Breadcrumb.tsx
│   │   ├── JsonLd.tsx
│   │   ├── OptimizedImage.tsx
│   │   ├── RelatedToolsSection.tsx
│   │   └── MetaTags.tsx
│   ├── blog/                                // 新增博客组件目录
│   │   ├── BlogPostCard.tsx
│   │   ├── BlogSidebar.tsx
│   │   ├── RelatedArticles.tsx
│   │   └── Pagination.tsx
│   ├── upload/
│   │   ├── FileUploader.tsx
│   │   ├── UploadProgress.tsx
│   │   └── ConfigurationModal.tsx
│   ├── quiz/
│   │   ├── QuizResults.tsx
│   │   ├── QuestionCard.tsx
│   │   ├── GenerationProgress.tsx
│   │   └── ExportOptions.tsx
│   ├── sections/
│   │   ├── HeroSection.tsx
│   │   ├── HowItWorks.tsx
│   │   ├── Testimonials.tsx
│   │   ├── Features.tsx
│   │   ├── FAQ.tsx
│   │   ├── TeacherUseCases.tsx             // 新增
│   │   ├── StudentFeatures.tsx             // 新增
│   │   └── GetStartedSection.tsx           // 新增
│   └── ui/
│       ├── Button.tsx
│       ├── Modal.tsx
│       └── Spinner.tsx
└── pages/
    ├── index.tsx (首页)
    ├── pdf-to-quiz.tsx                      // 新增
    ├── ai-quiz-generator.tsx                // 新增
    ├── mcq-generator.tsx                    // 新增
    ├── free-quiz-generator.tsx              // 新增
    ├── teachers.tsx                         // 新增
    ├── students.tsx                         // 新增
    ├── corporate-training.tsx               // 新增
    ├── blog/
    │   ├── index.tsx
    │   ├── [category]/
    │   │   └── index.tsx
    │   └── [...slug].tsx
    ├── sitemap.xml.tsx                      // 新增
    ├── robots.txt.tsx                       // 新增
    └── _app.tsx
```

这些补充内容为详细设计文档提供了完整的SEO实现方案，确保网站能够有效地覆盖目标关键词并获得良好的搜索引擎排名。
