# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "ghibli style ai generator"
NEXT_PUBLIC_SHOW_POWERED_BY = "false" 
# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = "https://smkcbuiphdrudfsglfua.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2NidWlwaGRydWRmc2dsZnVhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MDUxMzksImV4cCI6MjA2MDI4MTEzOX0.kf4q4iKiQpTGpbe4ZfAEwgZ0F76K-jlVbP1MLvLGcgo"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2NidWlwaGRydWRmc2dsZnVhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDcwNTEzOSwiZXhwIjoyMDYwMjgxMTM5fQ.vhC9D4SThO4ZshhFwhvFwn51QFZziVsKOD0rWS7fwLs"

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# 或gpt提示词:你帮我使用 openssl rand -base64 32 命令生成的一个新的随机密钥
# -----------------------------------------------------------------------------
AUTH_SECRET = "3Y1zv5OgH+Z8AsvRA4j7gSvBHz9NBmOqfTkaBHz7mkM="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "447882794353-0c20npagtct1g99887smmq8jmru2nni9.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "447882794353-0c20npagtct1g99887smmq8jmru2nni9.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = " "
AUTH_GITHUB_SECRET = " "
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

# Creem.io 支付配置
CREEM_API_KEY = creem_test_41QZPwHJVYuHuiwElkcPyr
CREEM_API_URL = https://test-api.creem.io
CREEM_MODE = test  # 或 live
CREEM_WEBHOOK_SECRET = whsec_7CYPuBL4Wg8UZWEwDwTJWV

BASIC_PLAN_PRODUCT_ID = prod_5hYNGlM2t3DdeKqs6AjW5K

# 支付页面配置
NEXT_PUBLIC_PAY_SUCCESS_URL = /creem-success
NEXT_PUBLIC_PAY_FAIL_URL = /fail
NEXT_PUBLIC_PAY_CANCEL_URL = /


NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

#AI-sdk
OPENROUTER_API_KEY = "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"
REPLICATE_API_TOKEN = "****************************************"